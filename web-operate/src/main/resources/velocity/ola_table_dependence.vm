#set($page_title='Ola数据库依赖关系')

<style>
    .dependence-chart-container {
        margin-top: 0;
        width: 100%;
        height: 60%;
        border: none;
        overflow: hidden;
    }
    .dependence-chart-iframe {
        width: 100%;
        height: 100%;
        border: none;
    }
    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        font-size: 16px;
        color: #909399;
    }
    .insert-sql-container {
        margin-top: 10px;
        height: 35%;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
    }
    .insert-sql-title {
        background-color: #f5f7fa;
        padding: 10px;
        border-bottom: 1px solid #dcdfe6;
        font-weight: bold;
        font-size: 14px;
    }
    .insert-sql-content {
        padding: 10px;
        height: calc(100% - 45px);
        overflow: auto;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        white-space: pre-wrap;
        background-color: #fafafa;
    }
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData(true)">
        <el-form-item label="">
            <el-autocomplete
                v-model="queryForm.tableName"
                :fetch-suggestions="queryTableNames"
                placeholder="查询表名"
                style="width: 500px"
                :trigger-on-focus="true"
                @select="handleTableSelect"
                clearable>
            </el-autocomplete>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item label="过滤Dim表">
            <el-switch v-model="queryForm.filterDim" @change="getData(true)"></el-switch>
        </el-form-item>
        <el-form-item label="反向依赖">
            <el-switch v-model="queryForm.reverseDependence" @change="getData(true)"></el-switch>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="getData(true)" :loading="queryLoading">查询</el-button>
            <el-button type="primary" @click="refreshDependence()" :loading="refreshLoading">重算依赖{{refreshSuccess ? '(成功)' : ''}}</el-button>
            <el-input
                v-model="tableNote"
                placeholder="表说明"
                style="width: 300px; margin-left: 10px;"
                v-if="queryForm.tableName"
                @focus="noteEditing = true">
            </el-input>
            <el-button
                v-if="noteEditing"
                type="success"
                size="small"
                style="margin-left: 10px;"
                @click="saveTableNote()"
                :loading="noteSaving">
                保存
            </el-button>
        </el-form-item>
    </el-form>

    <div class="dependence-chart-container">
        <div v-if="chartUrl" class="dependence-chart-iframe-container">
            <iframe :src="chartUrl" class="dependence-chart-iframe"></iframe>
        </div>
        <div v-else class="no-data">
            <span>{{noDataText}}</span>
        </div>
    </div>

    <div v-if="insertSql" class="insert-sql-container">
        <div class="insert-sql-title">表的INSERT SQL语句</div>
        <div class="insert-sql-content">{{insertSql}}</div>
    </div>
</div>

<script src="${_contextPath_}/js/vue-clipboard.min.js"></script>
<script>
var defaultQueryForm = {
    tableName: '$!{tableName}',
    filterDim: $!{filterDim},
    reverseDependence: $!{reverseDependence}
}
var vm = new Vue({
    el: '#app',
    data: {
        queryForm: Utils.copy(defaultQueryForm),
        refreshLoading: false,
        refreshSuccess: false,
        queryLoading: false,
        chartUrl: '',
        insertSql: '',
        noDataText: '请输入表名查询依赖关系图',
        tableNote: '',
        noteEditing: false,
        noteSaving: false
    },
    created: function () {
        // 页面加载时，从URL参数获取表名和其他参数
        var urlParams = new URLSearchParams(window.location.search);
        var tableNameFromUrl = urlParams.get('tableName');
        var filterDimFromUrl = urlParams.get('filterDim');
        var reverseDependenceFromUrl = urlParams.get('reverseDependence');

        if (tableNameFromUrl) {
            this.queryForm.tableName = tableNameFromUrl;
        }
        if (filterDimFromUrl !== null) {
            this.queryForm.filterDim = filterDimFromUrl === 'true';
        }
        if (reverseDependenceFromUrl !== null) {
            this.queryForm.reverseDependence = reverseDependenceFromUrl === 'true';
        }

        if (this.queryForm.tableName) {
            this.$nextTick(function() {
                this.getData(false);
            });
        }
    },
    methods: {
        updateUrlWithTableName: function(tableName) {
            if (window.parent !== window) { // iframe中
                if (tableName) {
                    var params = 'tableName=' + tableName;
                    if (this.queryForm.filterDim) {
                        params += '&filterDim=' + this.queryForm.filterDim;
                    }
                    if (this.queryForm.reverseDependence) {
                        params += '&reverseDependence=' + this.queryForm.reverseDependence;
                    }
                    window.parent.location.hash = '#/ola?' + params;
                } else {
                    window.parent.location.hash = '#/ola';
                }
            } else {
                if (tableName) {
                    var url = new URL(window.location.href);
                    url.searchParams.set('tableName', tableName);
                    url.searchParams.set('filterDim', this.queryForm.filterDim);
                    url.searchParams.set('reverseDependence', this.queryForm.reverseDependence);
                    window.history.pushState({}, '', url);
                } else {
                    // 如果没有表名，则移除URL参数
                    var url = new URL(window.location.href);
                    url.searchParams.delete('tableName');
                    url.searchParams.delete('filterDim');
                    url.searchParams.delete('reverseDependence');
                    window.history.pushState({}, '', url);
                }
            }
        },
        getData: function (updateUrl) {
            if (!this.queryForm.tableName) {
                Message.warning('请输入表名进行查询');
                return;
            }

            // 更新URL参数
            if (updateUrl) {
                this.updateUrlWithTableName(this.queryForm.tableName);
            }

            var that = this;
            this.queryLoading = true;
            this.chartUrl = '';
            this.insertSql = '';
            this.noDataText = '查询中，请稍候...';

            // 重置表说明相关状态
            this.tableNote = '';
            this.noteEditing = false;

            // 查询表的依赖关系图
            Resource.get("${_contextPath_}/ola/query_table_dependence", this.queryForm, function(response) {
                that.queryLoading = false;
                that.chartUrl = response.data.chartUrl;
                that.insertSql = response.data.insertSql;

                // 获取表说明信息
                that.getTableNote();
            }, function(error) {
                that.queryLoading = false;
                that.noDataText = '查询异常:' + error.msg;
            });
        },
        refreshDependence: function () {
            var that = this;
            this.refreshLoading = true;

            Message.confirm('确定要重新计算所有表的依赖关系吗?', function() {
                this.refreshSuccess = false;
                Resource.post("${_contextPath_}/ola/refresh_dependence", {}, function() {
                    that.refreshLoading = false
                    that.refreshSuccess = true
                    Message.success('刷新依赖关系成功！')
                })
            }, function () {
                that.refreshLoading = false;
            })
        },
        queryTableNames(query, callback) {
            if (!query) {
                return callback([]);
            }
            var that = this;
            Resource.get("${_contextPath_}/ola/query_table_names", {query: query}, function(response) {
                var suggestions = response.data.map(function(item) {
                    return { value: item.tableName };
                });
                callback(suggestions);
            }, function(error) {
                callback([]);
            });
        },
        handleTableSelect(item) {
            this.queryForm.tableName = item.value;
            this.getData(true);
        },
        // 获取表说明信息
        getTableNote: function() {
            if (!this.queryForm.tableName) {
                return;
            }
            var that = this;
            Resource.get("${_contextPath_}/ola/get_table_note", {
                tableName: this.queryForm.tableName
            }, function(response) {
                that.tableNote = response.data || '';
            }, function(error) {
                Message.error('获取表说明失败:' + error.msg);
            });
        },
        // 保存表说明信息
        saveTableNote: function() {
            var that = this;
            this.noteSaving = true;

            Resource.post("${_contextPath_}/ola/update_table_note", {
                tableName: this.queryForm.tableName,
                note: this.tableNote
            }, function() {
                that.noteSaving = false;
                that.noteEditing = false;
                Message.success('表说明保存成功');
            }, function(error) {
                that.noteSaving = false;
                Message.error('保存失败:' + error.msg);
            });
        }
    }
})
</script>